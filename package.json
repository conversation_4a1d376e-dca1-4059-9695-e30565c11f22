{"name": "text-converter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@lexical/code": "^0.33.0", "@lexical/history": "^0.33.0", "@lexical/html": "^0.33.0", "@lexical/link": "^0.33.0", "@lexical/list": "^0.33.0", "@lexical/markdown": "^0.33.0", "@lexical/react": "^0.33.0", "@lexical/rich-text": "^0.33.0", "@lexical/table": "^0.33.0", "@lexical/text": "^0.33.0", "@lexical/utils": "^0.33.0", "@tiptap/extension-color": "^2.25.0", "@tiptap/extension-font-family": "^2.25.0", "@tiptap/extension-image": "^2.25.0", "@tiptap/extension-link": "^2.25.0", "@tiptap/extension-table": "^2.25.0", "@tiptap/extension-table-cell": "^2.25.0", "@tiptap/extension-table-header": "^2.25.0", "@tiptap/extension-table-row": "^2.25.0", "@tiptap/extension-text-align": "^2.25.0", "@tiptap/extension-text-style": "^2.25.0", "@tiptap/pm": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "lexical": "^0.33.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.3"}}