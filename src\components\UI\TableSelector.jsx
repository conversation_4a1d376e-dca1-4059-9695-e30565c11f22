import React, { useState } from 'react';

const TableSelector = ({ onTableCreate, onClose }) => {
    const [rows, setRows] = useState(3);
    const [cols, setCols] = useState(3);
    const [hoveredCell, setHoveredCell] = useState({ row: 3, col: 3 });

    const handleCellHover = (row, col) => {
        setHoveredCell({ row, col });
        setRows(row);
        setCols(col);
    };

    const handleTableCreate = () => {
        onTableCreate(rows, cols);
        onClose();
    };

    const renderGrid = () => {
        const grid = [];
        for (let row = 1; row <= 10; row++) {
            const rowCells = [];
            for (let col = 1; col <= 10; col++) {
                const isSelected = row <= hoveredCell.row && col <= hoveredCell.col;
                rowCells.push(
                    <div
                        key={`${row}-${col}`}
                        className={`table-cell ${isSelected ? 'selected' : ''}`}
                        onMouseEnter={() => handleCellHover(row, col)}
                        onClick={handleTableCreate}
                    />
                );
            }
            grid.push(
                <div key={row} className="table-row">
                    {rowCells}
                </div>
            );
        }
        return grid;
    };

    return (
        <div className="table-selector-overlay" onClick={onClose}>
            <div className="table-selector" onClick={(e) => e.stopPropagation()}>
                <div className="table-selector-header">
                    <h4>Insert Table</h4>
                    <button className="close-btn" onClick={onClose}>×</button>
                </div>
                <div className="table-grid">
                    {renderGrid()}
                </div>
                <div className="table-info">
                    {rows} × {cols} Table
                </div>
                <div className="table-selector-footer">
                    <div className="custom-size">
                        <label>
                            Rows: 
                            <input 
                                type="number" 
                                min="1" 
                                max="20" 
                                value={rows} 
                                onChange={(e) => setRows(parseInt(e.target.value) || 1)}
                            />
                        </label>
                        <label>
                            Columns: 
                            <input 
                                type="number" 
                                min="1" 
                                max="20" 
                                value={cols} 
                                onChange={(e) => setCols(parseInt(e.target.value) || 1)}
                            />
                        </label>
                    </div>
                    <button className="create-table-btn" onClick={handleTableCreate}>
                        Create Table
                    </button>
                </div>
            </div>
        </div>
    );
};

export default TableSelector;
