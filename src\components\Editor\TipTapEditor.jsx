import { Color } from '@tiptap/extension-color';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Table from '@tiptap/extension-table';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TableRow from '@tiptap/extension-table-row';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import React, { useState } from 'react';
import '../styles/editor.css';

const TipTapEditor = () => {
    const [htmlContent, setHtmlContent] = useState('<p>Start typing...</p>');

    const editor = useEditor({
        extensions: [
            StarterKit,
            TextStyle,
            Color,
            TextAlign.configure({
                types: ['heading', 'paragraph'],
            }),
            Table.configure({
                resizable: true,
            }),
            TableRow,
            TableHeader,
            TableCell,
            Image,
            Link.configure({
                openOnClick: false,
            }),
        ],
        content: '<p>Start typing...</p>',
        onUpdate: ({ editor }) => {
            const html = editor.getHTML();
            console.log('HTML updated:', html); // Debug log
            setHtmlContent(html);
        },
        onCreate: ({ editor }) => {
            // Set initial HTML content when editor is created
            const html = editor.getHTML();
            setHtmlContent(html);
        },
    });

    const handleSaveAsHTML = () => {
        const blob = new Blob([htmlContent], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'content.html';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    const handleCleanEditor = () => {
        editor?.commands.clearContent();
    };

    // Toolbar functions
    const toggleBold = () => editor?.chain().focus().toggleBold().run();
    const toggleItalic = () => editor?.chain().focus().toggleItalic().run();
    const toggleUnderline = () => editor?.chain().focus().toggleUnderline().run();
    const toggleStrike = () => editor?.chain().focus().toggleStrike().run();
    const toggleCode = () => editor?.chain().focus().toggleCode().run();

    const setHeading = (level) => {
        if (level === 0) {
            editor?.chain().focus().setParagraph().run();
        } else {
            editor?.chain().focus().toggleHeading({ level }).run();
        }
    };

    const toggleBulletList = () => editor?.chain().focus().toggleBulletList().run();
    const toggleOrderedList = () => editor?.chain().focus().toggleOrderedList().run();
    const toggleBlockquote = () => editor?.chain().focus().toggleBlockquote().run();
    const toggleCodeBlock = () => editor?.chain().focus().toggleCodeBlock().run();

    const setTextAlign = (alignment) => editor?.chain().focus().setTextAlign(alignment).run();

    const insertTable = () => {
        editor?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
    };

    const insertImage = () => {
        const url = prompt('Enter image URL:');
        if (url) {
            editor?.chain().focus().setImage({ src: url }).run();
        }
    };

    const insertLink = () => {
        const url = prompt('Enter URL:');
        if (url) {
            editor?.chain().focus().setLink({ href: url }).run();
        }
    };

    const undo = () => editor?.chain().focus().undo().run();
    const redo = () => editor?.chain().focus().redo().run();

    if (!editor) {
        return <div>Loading editor...</div>;
    }

    return (
        <div className="editor-container">
            <div className="editor-header">
                <h1>HTML5 WYSIWYG Editor</h1>
                <div className="header-controls">
                    <button className="clean-button" onClick={handleCleanEditor}>🧹 Clean</button>
                    <button className="save-button" onClick={handleSaveAsHTML}>
                        💾 Save HTML
                    </button>
                </div>
            </div>

            <div className="editor-wrapper">
                {/* Toolbar */}
                <div className="toolbar">
                    {/* Text Formatting */}
                    <div className="toolbar-group">
                        <button
                            className={`toolbar-button ${editor.isActive('bold') ? 'active' : ''}`}
                            onClick={toggleBold}
                            title="Bold"
                        >
                            <strong>B</strong>
                        </button>
                        <button
                            className={`toolbar-button ${editor.isActive('italic') ? 'active' : ''}`}
                            onClick={toggleItalic}
                            title="Italic"
                        >
                            <em>I</em>
                        </button>
                        <button
                            className={`toolbar-button ${editor.isActive('underline') ? 'active' : ''}`}
                            onClick={toggleUnderline}
                            title="Underline"
                        >
                            <u>U</u>
                        </button>
                        <button
                            className={`toolbar-button ${editor.isActive('strike') ? 'active' : ''}`}
                            onClick={toggleStrike}
                            title="Strikethrough"
                        >
                            <s>S</s>
                        </button>
                        <button
                            className={`toolbar-button ${editor.isActive('code') ? 'active' : ''}`}
                            onClick={toggleCode}
                            title="Code"
                        >
                            &lt;/&gt;
                        </button>
                    </div>

                    {/* Block Types */}
                    <div className="toolbar-group">
                        <select
                            className="toolbar-select"
                            value={
                                editor.isActive('heading', { level: 1 }) ? 'h1' :
                                    editor.isActive('heading', { level: 2 }) ? 'h2' :
                                        editor.isActive('heading', { level: 3 }) ? 'h3' :
                                            editor.isActive('heading', { level: 4 }) ? 'h4' :
                                                editor.isActive('heading', { level: 5 }) ? 'h5' :
                                                    editor.isActive('heading', { level: 6 }) ? 'h6' :
                                                        'paragraph'
                            }
                            onChange={(e) => {
                                const value = e.target.value;
                                if (value === 'paragraph') {
                                    setHeading(0);
                                } else {
                                    const level = parseInt(value.replace('h', ''));
                                    setHeading(level);
                                }
                            }}
                        >
                            <option value="paragraph">Paragraph</option>
                            <option value="h1">Heading 1</option>
                            <option value="h2">Heading 2</option>
                            <option value="h3">Heading 3</option>
                            <option value="h4">Heading 4</option>
                            <option value="h5">Heading 5</option>
                            <option value="h6">Heading 6</option>
                        </select>
                    </div>

                    {/* Lists */}
                    <div className="toolbar-group">
                        <button
                            className={`toolbar-button ${editor.isActive('bulletList') ? 'active' : ''}`}
                            onClick={toggleBulletList}
                            title="Bullet List"
                        >
                            •
                        </button>
                        <button
                            className={`toolbar-button ${editor.isActive('orderedList') ? 'active' : ''}`}
                            onClick={toggleOrderedList}
                            title="Numbered List"
                        >
                            1.
                        </button>
                    </div>

                    {/* Alignment */}
                    <div className="toolbar-group">
                        <button
                            className={`toolbar-button ${editor.isActive({ textAlign: 'left' }) ? 'active' : ''}`}
                            onClick={() => setTextAlign('left')}
                            title="Align Left"
                        >
                            ⬅
                        </button>
                        <button
                            className={`toolbar-button ${editor.isActive({ textAlign: 'center' }) ? 'active' : ''}`}
                            onClick={() => setTextAlign('center')}
                            title="Align Center"
                        >
                            ↔
                        </button>
                        <button
                            className={`toolbar-button ${editor.isActive({ textAlign: 'right' }) ? 'active' : ''}`}
                            onClick={() => setTextAlign('right')}
                            title="Align Right"
                        >
                            ➡
                        </button>
                    </div>

                    {/* Insert Elements */}
                    <div className="toolbar-group">
                        <button
                            className="toolbar-button"
                            onClick={insertLink}
                            title="Insert Link"
                        >
                            🔗
                        </button>
                        <button
                            className="toolbar-button"
                            onClick={insertImage}
                            title="Insert Image"
                        >
                            🖼️
                        </button>
                        <button
                            className="toolbar-button"
                            onClick={insertTable}
                            title="Insert Table"
                        >
                            📊
                        </button>
                        <button
                            className={`toolbar-button ${editor.isActive('blockquote') ? 'active' : ''}`}
                            onClick={toggleBlockquote}
                            title="Quote"
                        >
                            ❝
                        </button>
                        <button
                            className={`toolbar-button ${editor.isActive('codeBlock') ? 'active' : ''}`}
                            onClick={toggleCodeBlock}
                            title="Code Block"
                        >
                            { }
                        </button>
                    </div>

                    {/* Undo/Redo */}
                    <div className="toolbar-group">
                        <button
                            className="toolbar-button"
                            onClick={undo}
                            title="Undo"
                        >
                            ↶
                        </button>
                        <button
                            className="toolbar-button"
                            onClick={redo}
                            title="Redo"
                        >
                            ↷
                        </button>
                    </div>
                </div>

                <div className="editor-split-view">
                    {/* Left Panel - Rich Text Editor */}
                    <div className="editor-panel">
                        <div className="panel-header">
                            <h3>📝 Visual Editor</h3>
                        </div>
                        <div className="editor-content">
                            <EditorContent editor={editor} className="tiptap-editor" />
                        </div>
                    </div>

                    {/* Right Panel - HTML Preview */}
                    <div className="preview-panel">
                        <div className="panel-header">
                            <h3>👁️ HTML Preview</h3>
                            <div style={{ fontSize: '0.8rem', color: '#666' }}>
                                HTML Length: {htmlContent.length} chars
                            </div>
                        </div>
                        <div className="preview-content">
                            <div
                                className="preview-html"
                                style={{
                                    fontFamily: 'Arial, sans-serif',
                                    lineHeight: '1.6',
                                    padding: '20px',
                                    color: '#333',
                                    height: '100%',
                                    overflow: 'auto',
                                    backgroundColor: 'white'
                                }}
                                dangerouslySetInnerHTML={{ __html: htmlContent }}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TipTapEditor;
