import React, { useState } from 'react';

const FindReplace = ({ onReplace, onClose }) => {
    const [replaceRules, setReplaceRules] = useState([
        { find: '', replace: '', id: 1 }
    ]);

    const addReplaceRule = () => {
        const newId = Math.max(...replaceRules.map(r => r.id)) + 1;
        setReplaceRules([...replaceRules, { find: '', replace: '', id: newId }]);
    };

    const removeReplaceRule = (id) => {
        if (replaceRules.length > 1) {
            setReplaceRules(replaceRules.filter(rule => rule.id !== id));
        }
    };

    const updateReplaceRule = (id, field, value) => {
        setReplaceRules(replaceRules.map(rule => 
            rule.id === id ? { ...rule, [field]: value } : rule
        ));
    };

    const handleReplaceAll = () => {
        const validRules = replaceRules.filter(rule => rule.find.trim() !== '');
        onReplace(validRules);
        onClose();
    };

    return (
        <div className="find-replace-overlay" onClick={onClose}>
            <div className="find-replace" onClick={(e) => e.stopPropagation()}>
                <div className="find-replace-header">
                    <h4>🔍 Find and Replace</h4>
                    <button className="close-btn" onClick={onClose}>×</button>
                </div>
                <div className="find-replace-content">
                    <div className="replace-rules">
                        {replaceRules.map((rule, index) => (
                            <div key={rule.id} className="replace-rule">
                                <div className="rule-number">{index + 1}</div>
                                <div className="rule-inputs">
                                    <input
                                        type="text"
                                        placeholder="Find..."
                                        value={rule.find}
                                        onChange={(e) => updateReplaceRule(rule.id, 'find', e.target.value)}
                                        className="find-input"
                                    />
                                    <input
                                        type="text"
                                        placeholder="Replace with..."
                                        value={rule.replace}
                                        onChange={(e) => updateReplaceRule(rule.id, 'replace', e.target.value)}
                                        className="replace-input"
                                    />
                                </div>
                                {replaceRules.length > 1 && (
                                    <button 
                                        className="remove-rule-btn"
                                        onClick={() => removeReplaceRule(rule.id)}
                                        title="Remove rule"
                                    >
                                        ×
                                    </button>
                                )}
                            </div>
                        ))}
                    </div>
                    <button className="add-rule-btn" onClick={addReplaceRule}>
                        + Add Rule
                    </button>
                </div>
                <div className="find-replace-footer">
                    <div className="replace-info">
                        {replaceRules.filter(r => r.find.trim()).length} active rules
                    </div>
                    <button className="replace-all-btn" onClick={handleReplaceAll}>
                        Replace All
                    </button>
                </div>
            </div>
        </div>
    );
};

export default FindReplace;
