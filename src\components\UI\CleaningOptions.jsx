import React, { useState } from 'react';

const CleaningOptions = ({ onClean, onClose }) => {
    const [options, setOptions] = useState({
        clearInlineStyles: false,
        clearClassesAndIds: false,
        clearComments: false,
        clearSpanTags: false,
        clearSuccessiveNbsp: false,
        clearTagsWithOneNbsp: false,
        clearEmptyTags: false,
        clearTagAttributes: false,
        clearAllTags: false,
        clearImages: false,
        clearLinks: false,
        clearTables: false,
        convertTablesToDivs: false,
        organizeTreeView: false,
        convertBoldItalic: false
    });

    const handleOptionChange = (option) => {
        setOptions(prev => ({
            ...prev,
            [option]: !prev[option]
        }));
    };

    const handleClean = () => {
        onClean(options);
        onClose();
    };

    const cleaningOptionsList = [
        { key: 'clearInlineStyles', label: 'Clear inline styles' },
        { key: 'clearClassesAndIds', label: 'Clear classes and IDs' },
        { key: 'clearComments', label: 'Clear comments' },
        { key: 'clearSpanTags', label: 'Clear span tags' },
        { key: 'clearSuccessiveNbsp', label: 'Clear successive &nbsp;s' },
        { key: 'clearTagsWithOneNbsp', label: 'Clear tags with one &nbsp;' },
        { key: 'clearEmptyTags', label: 'Clear empty tags' },
        { key: 'clearTagAttributes', label: 'Clear tag attributes' },
        { key: 'clearAllTags', label: 'Clear all tags' },
        { key: 'clearImages', label: 'Clear images' },
        { key: 'clearLinks', label: 'Clear links' },
        { key: 'clearTables', label: 'Clear tables' },
        { key: 'convertTablesToDivs', label: 'Convert tables to <div>s' },
        { key: 'organizeTreeView', label: 'Organize tree-view' },
        { key: 'convertBoldItalic', label: 'Convert <b> to <strong>, <i> to <em>' }
    ];

    return (
        <div className="cleaning-options-overlay" onClick={onClose}>
            <div className="cleaning-options" onClick={(e) => e.stopPropagation()}>
                <div className="cleaning-options-header">
                    <h4>🧹 Cleaning Options</h4>
                    <button className="close-btn" onClick={onClose}>×</button>
                </div>
                <div className="cleaning-options-content">
                    {cleaningOptionsList.map(option => (
                        <label key={option.key} className="cleaning-option">
                            <input
                                type="checkbox"
                                checked={options[option.key]}
                                onChange={() => handleOptionChange(option.key)}
                            />
                            <span>{option.label}</span>
                        </label>
                    ))}
                </div>
                <div className="cleaning-options-footer">
                    <button className="select-all-btn" onClick={() => {
                        const allSelected = Object.values(options).every(v => v);
                        const newOptions = {};
                        Object.keys(options).forEach(key => {
                            newOptions[key] = !allSelected;
                        });
                        setOptions(newOptions);
                    }}>
                        {Object.values(options).every(v => v) ? 'Deselect All' : 'Select All'}
                    </button>
                    <button className="clean-btn" onClick={handleClean}>
                        Clean HTML
                    </button>
                </div>
            </div>
        </div>
    );
};

export default CleaningOptions;
