import { TRANSFORMERS } from '@lexical/markdown';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { MarkdownShortcutPlugin } from '@lexical/react/LexicalMarkdownShortcutPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { TablePlugin } from '@lexical/react/LexicalTablePlugin';
import { $getRoot } from 'lexical';
import React, { useState } from 'react';

import { editorConfig } from '../../utils/editorConfig';
import { saveAsHTML } from '../../utils/editorUtils';
import EditorRefPlugin from '../plugins/EditorRefPlugin';
import '../styles/editor.css';
import Toolbar from './Toolbar';

// Main Editor Component
const LexicalEditor = () => {
    const [htmlContent, setHtmlContent] = useState('');
    const [editorRef, setEditorRef] = useState(null);

    // Inject dynamic styles on component mount
    React.useEffect(() => {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = `
            .editor-container {
                transition: all 0.3s ease;
            }

            .toolbar-button {
                transition: all 0.2s ease;
            }

            .toolbar-button:active {
                transform: scale(0.95);
            }

            .editor-input:focus {
                box-shadow: inset 0 0 0 2px #007bff;
            }

            .save-button {
                transition: all 0.2s ease;
            }

            .save-button:active {
                transform: translateY(1px);
            }
        `;
        document.head.appendChild(styleSheet);

        return () => {
            document.head.removeChild(styleSheet);
        };
    }, []);

    const handleEditorChange = (editorState) => {
        editorState.read(() => {
            const root = $getRoot();
            const htmlString = root.getTextContent();
            setHtmlContent(htmlString);
        });
    };

    const handleSaveAsHTML = () => {
        saveAsHTML(htmlContent);
    };

    return (
        <div className="editor-container">
            <div className="editor-header">
                <h1>HTML5 WYSIWYG Editor</h1>
                <button className="save-button" onClick={handleSaveAsHTML}>
                    Save as HTML
                </button>
            </div>

            <LexicalComposer initialConfig={editorConfig}>
                <div className="editor-wrapper">
                    <Toolbar editor={editorRef} />
                    <div className="editor-content">
                        <RichTextPlugin
                            contentEditable={
                                <ContentEditable
                                    className="editor-input"
                                    placeholder="Start typing..."
                                />
                            }
                            placeholder={
                                <div className="editor-placeholder">
                                    Start typing...
                                </div>
                            }
                            ErrorBoundary={({ children }) => children}
                        />
                        <OnChangePlugin onChange={handleEditorChange} />
                        <HistoryPlugin />
                        <AutoFocusPlugin />
                        <LinkPlugin />
                        <ListPlugin />
                        <TablePlugin />
                        <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
                        <EditorRefPlugin onRef={setEditorRef} />
                    </div>
                </div>
            </LexicalComposer>
        </div>
    );
};

export default LexicalEditor;