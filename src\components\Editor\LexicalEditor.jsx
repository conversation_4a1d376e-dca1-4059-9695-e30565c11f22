import { $generateHtmlFromNodes } from '@lexical/html';
import { TRANSFORMERS } from '@lexical/markdown';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { MarkdownShortcutPlugin } from '@lexical/react/LexicalMarkdownShortcutPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { TablePlugin } from '@lexical/react/LexicalTablePlugin';
import { CLEAR_EDITOR_COMMAND } from 'lexical';
import React, { useState } from 'react';

import { editorConfig } from '../../utils/editorConfig';
import { saveAsHTML } from '../../utils/editorUtils';
import EditorRefPlugin from '../plugins/EditorRefPlugin';
import '../styles/editor.css';
import Toolbar from './Toolbar';

// Main Editor Component
const LexicalEditor = () => {
    const [htmlContent, setHtmlContent] = useState('');
    const [editorRef, setEditorRef] = useState(null);

    // Inject dynamic styles on component mount
    React.useEffect(() => {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = `
            .editor-container {
                transition: all 0.3s ease;
            }

            .toolbar-button {
                transition: all 0.2s ease;
            }

            .toolbar-button:active {
                transform: scale(0.95);
            }

            .editor-input:focus {
                box-shadow: inset 0 0 0 2px #007bff;
            }

            .save-button {
                transition: all 0.2s ease;
            }

            .save-button:active {
                transform: translateY(1px);
            }
        `;
        document.head.appendChild(styleSheet);

        return () => {
            document.head.removeChild(styleSheet);
        };
    }, []);

    const handleEditorChange = (editorState) => {
        if (!editorRef) return;

        editorState.read(() => {
            // Use the correct Lexical API according to documentation
            const htmlString = $generateHtmlFromNodes(editorRef, null);
            setHtmlContent(htmlString);
        });
    };

    const handleSaveAsHTML = () => {
        saveAsHTML(htmlContent);
    };

    const handleCleanEditor = () => {
        if (!editorRef) return;
        editorRef.dispatchCommand(CLEAR_EDITOR_COMMAND, undefined);
    };

    return (
        <div className="editor-container">
            <div className="editor-header">
                <h1>HTML5 WYSIWYG Editor</h1>
                <div className="header-controls">
                    <button className="clean-button" onClick={handleCleanEditor}>🧹 Clean</button>
                    <button className="save-button" onClick={handleSaveAsHTML}>
                        💾 Save HTML
                    </button>
                </div>
            </div>

            <LexicalComposer initialConfig={editorConfig}>
                <div className="editor-wrapper">
                    <Toolbar editor={editorRef} />
                    <div className="editor-split-view">
                        {/* Left Panel - Rich Text Editor */}
                        <div className="editor-panel">
                            <div className="panel-header">
                                <h3>📝 Visual Editor</h3>
                            </div>
                            <div className="editor-content">
                                <RichTextPlugin
                                    contentEditable={
                                        <ContentEditable
                                            className="editor-input"
                                            placeholder="Start typing..."
                                        />
                                    }
                                    placeholder={
                                        <div className="editor-placeholder">
                                            Start typing...
                                        </div>
                                    }
                                    ErrorBoundary={({ children }) => children}
                                />
                                <OnChangePlugin onChange={handleEditorChange} />
                                <HistoryPlugin />
                                <AutoFocusPlugin />
                                <LinkPlugin />
                                <ListPlugin />
                                <TablePlugin />
                                <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
                                <EditorRefPlugin onRef={setEditorRef} />
                            </div>
                        </div>

                        {/* Right Panel - HTML Preview */}
                        <div className="preview-panel">
                            <div className="panel-header">
                                <h3>👁️ HTML Preview</h3>
                                <div className="preview-controls">
                                    <button className="view-source-btn">📄 View Source</button>
                                </div>
                            </div>
                            <div className="preview-content">
                                <iframe
                                    className="preview-iframe"
                                    srcDoc={`
                                        <!DOCTYPE html>
                                        <html>
                                        <head>
                                            <meta charset="UTF-8">
                                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                            <style>
                                                body {
                                                    font-family: Arial, sans-serif;
                                                    line-height: 1.6;
                                                    margin: 20px;
                                                    color: #333;
                                                }
                                                table { border-collapse: collapse; width: 100%; }
                                                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                                                th { background-color: #f2f2f2; }
                                                blockquote {
                                                    border-left: 4px solid #007bff;
                                                    padding-left: 16px;
                                                    margin: 16px 0;
                                                    font-style: italic;
                                                    color: #666;
                                                }
                                                code {
                                                    background-color: #f1f3f4;
                                                    padding: 2px 4px;
                                                    border-radius: 3px;
                                                    font-family: monospace;
                                                }
                                                pre {
                                                    background-color: #f8f9fa;
                                                    border: 1px solid #e9ecef;
                                                    border-radius: 4px;
                                                    padding: 12px;
                                                    overflow-x: auto;
                                                }
                                            </style>
                                        </head>
                                        <body>
                                            ${htmlContent || ''}
                                        </body>
                                        </html>
                                    `}
                                    title="HTML Preview"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </LexicalComposer>
        </div>
    );
};

export default LexicalEditor;