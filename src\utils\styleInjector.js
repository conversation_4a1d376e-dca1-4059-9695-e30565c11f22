// Dynamic style injection utility
export const injectStyles = (styles) => {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
  return styleSheet;
};

// Additional dynamic styles that can be injected
export const dynamicStyles = `
  .editor-container {
    transition: all 0.3s ease;
  }
  
  .toolbar-button {
    transition: all 0.2s ease;
  }
  
  .toolbar-button:active {
    transform: scale(0.95);
  }
  
  .editor-input:focus {
    box-shadow: inset 0 0 0 2px #007bff;
  }
  
  .save-button {
    transition: all 0.2s ease;
  }
  
  .save-button:active {
    transform: translateY(1px);
  }
`;
