import React, { useRef } from 'react';

const FileUpload = ({ onFileSelect, accept = "image/*", children }) => {
    const fileInputRef = useRef(null);

    const handleClick = () => {
        fileInputRef.current?.click();
    };

    const handleFileChange = (event) => {
        const file = event.target.files[0];
        if (file && onFileSelect) {
            onFileSelect(file);
        }
    };

    return (
        <>
            <input
                type="file"
                ref={fileInputRef}
                style={{ display: 'none' }}
                accept={accept}
                onChange={handleFileChange}
            />
            <button
                type="button"
                onClick={handleClick}
                className="toolbar-button"
            >
                {children}
            </button>
        </>
    );
};

export default FileUpload;