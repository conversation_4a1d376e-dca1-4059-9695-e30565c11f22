import React, { useState } from 'react';

const TableCreator = ({ onCreateTable, onClose }) => {
    const [rows, setRows] = useState(3);
    const [cols, setCols] = useState(3);
    const [withHeaderRow, setWithHeaderRow] = useState(true);
    const [hoveredCell, setHoveredCell] = useState({ row: 3, col: 3 });

    const handleCellHover = (row, col) => {
        setHoveredCell({ row, col });
        setRows(row);
        setCols(col);
    };

    const handleCreateTable = () => {
        onCreateTable({ rows, cols, withHeaderRow });
        onClose();
    };

    const renderGrid = () => {
        const grid = [];
        for (let row = 1; row <= 8; row++) {
            const rowCells = [];
            for (let col = 1; col <= 10; col++) {
                const isSelected = row <= hoveredCell.row && col <= hoveredCell.col;
                const isHeader = withHeaderRow && row === 1 && isSelected;
                rowCells.push(
                    <div
                        key={`${row}-${col}`}
                        className={`table-grid-cell ${isSelected ? 'selected' : ''} ${isHeader ? 'header' : ''}`}
                        onMouseEnter={() => handleCellHover(row, col)}
                        onClick={handleCreateTable}
                        title={`${row} × ${col} table`}
                    />
                );
            }
            grid.push(
                <div key={row} className="table-grid-row">
                    {rowCells}
                </div>
            );
        }
        return grid;
    };

    return (
        <div className="table-creator-overlay" onClick={onClose}>
            <div className="table-creator" onClick={(e) => e.stopPropagation()}>
                <div className="table-creator-header">
                    <h4>📊 Insert Table</h4>
                    <button className="close-btn" onClick={onClose}>×</button>
                </div>
                
                <div className="table-grid-container">
                    <div className="table-grid">
                        {renderGrid()}
                    </div>
                    <div className="table-info">
                        {rows} × {cols} Table
                        {withHeaderRow && ' (with header)'}
                    </div>
                </div>

                <div className="table-options">
                    <label className="table-option">
                        <input
                            type="checkbox"
                            checked={withHeaderRow}
                            onChange={(e) => setWithHeaderRow(e.target.checked)}
                        />
                        Include header row
                    </label>
                </div>

                <div className="table-custom-size">
                    <div className="size-inputs">
                        <label>
                            Rows:
                            <input
                                type="number"
                                min="1"
                                max="20"
                                value={rows}
                                onChange={(e) => setRows(parseInt(e.target.value) || 1)}
                            />
                        </label>
                        <label>
                            Columns:
                            <input
                                type="number"
                                min="1"
                                max="20"
                                value={cols}
                                onChange={(e) => setCols(parseInt(e.target.value) || 1)}
                            />
                        </label>
                    </div>
                </div>

                <div className="table-creator-footer">
                    <button className="cancel-btn" onClick={onClose}>
                        Cancel
                    </button>
                    <button className="create-btn" onClick={handleCreateTable}>
                        Insert Table
                    </button>
                </div>
            </div>
        </div>
    );
};

export default TableCreator;
