import { $createTableNodeWithDimensions } from '@lexical/table';
import { $insertNodeToNearestRoot, mergeRegister } from '@lexical/utils';
import {
    $getSelection,
    $isRangeSelection,
    CLEAR_EDITOR_COMMAND,
    FORMAT_ELEMENT_COMMAND,
    FORMAT_TEXT_COMMAND,
    INDENT_CONTENT_COMMAND,
    OUTDENT_CONTENT_COMMAND,
    REDO_COMMAND,
    SELECTION_CHANGE_COMMAND,
    UNDO_COMMAND
} from 'lexical';
import React, { useCallback, useState } from 'react';
import {
    insertCode,
    insertHeading,
    insertHorizontalRule,
    insertLink,
    insertList,
    insertQuote
} from '../../utils/editorUtils';
import ImagePlugin from '../plugins/ImagePlugin';
import ColorPicker from '../UI/ColorPicker';

const Toolbar = ({ editor }) => {
    const [activeFormats, setActiveFormats] = useState(new Set());
    const [blockType, setBlockType] = useState('paragraph');
    const [fontSize, setFontSize] = useState('16px');
    const [fontFamily, setFontFamily] = useState('Arial');
    const [textColor, setTextColor] = useState('#000000');
    const [backgroundColor, setBackgroundColor] = useState('#ffffff');

    // Modal states
    const [showTableSelector, setShowTableSelector] = useState(false);
    const [showCleaningOptions, setShowCleaningOptions] = useState(false);
    const [showFindReplace, setShowFindReplace] = useState(false);

    const updateToolbar = useCallback(() => {
        if (!editor) {
            return;
        }

        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
            const anchorNode = selection.anchor.getNode();
            const element = anchorNode.getKey() === 'root' ? anchorNode : anchorNode.getTopLevelElementOrThrow();
            const elementKey = element.getKey();
            const elementDOM = editor.getElementByKey(elementKey);

            if (elementDOM !== null) {
                const formats = new Set();
                if (selection.hasFormat('bold')) formats.add('bold');
                if (selection.hasFormat('italic')) formats.add('italic');
                if (selection.hasFormat('underline')) formats.add('underline');
                if (selection.hasFormat('strikethrough')) formats.add('strikethrough');
                if (selection.hasFormat('subscript')) formats.add('subscript');
                if (selection.hasFormat('superscript')) formats.add('superscript');

                setActiveFormats(formats);

                // Update block type
                if (element.getType() === 'heading') {
                    setBlockType(element.getTag());
                } else if (element.getType() === 'quote') {
                    setBlockType('quote');
                } else if (element.getType() === 'code') {
                    setBlockType('code');
                } else {
                    setBlockType('paragraph');
                }
            }
        }
    }, [editor]);

    React.useEffect(() => {
        if (!editor) {
            return;
        }

        return mergeRegister(
            editor.registerUpdateListener(({ editorState }) => {
                editorState.read(() => {
                    updateToolbar();
                });
            }),
            editor.registerCommand(
                SELECTION_CHANGE_COMMAND,
                () => {
                    updateToolbar();
                    return false;
                },
                1
            )
        );
    }, [editor, updateToolbar]);

    const formatText = (format) => {
        if (!editor) return;
        editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
    };

    const formatElement = (format) => {
        if (!editor) return;
        editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, format);
    };

    const handleBlockTypeChange = (value) => {
        if (!editor) return;
        if (value.startsWith('h')) {
            insertHeading(editor, value);
        } else if (value === 'quote') {
            insertQuote(editor);
        } else if (value === 'code') {
            insertCode(editor);
        }
    };

    const indent = () => {
        if (!editor) return;
        editor.dispatchCommand(INDENT_CONTENT_COMMAND, undefined);
    };

    const outdent = () => {
        if (!editor) return;
        editor.dispatchCommand(OUTDENT_CONTENT_COMMAND, undefined);
    };

    const undo = () => {
        if (!editor) return;
        editor.dispatchCommand(UNDO_COMMAND, undefined);
    };

    const redo = () => {
        if (!editor) return;
        editor.dispatchCommand(REDO_COMMAND, undefined);
    };

    const clearEditor = () => {
        if (!editor) return;
        editor.dispatchCommand(CLEAR_EDITOR_COMMAND, undefined);
    };

    // Enhanced table creation
    const handleTableCreate = (rows, cols) => {
        if (!editor) return;
        editor.update(() => {
            const selection = $getSelection();
            if ($isRangeSelection(selection)) {
                const table = $createTableNodeWithDimensions(rows, cols, false);
                $insertNodeToNearestRoot(table);
            }
        });
    };

    // Cleaning functionality
    const handleClean = (options) => {
        if (!editor) return;
        // Implementation for cleaning HTML based on options
        console.log('Cleaning with options:', options);
        // This would need to be implemented based on the specific cleaning requirements
    };

    // Find and replace functionality
    const handleFindReplace = (rules) => {
        if (!editor) return;
        // Implementation for find and replace
        console.log('Find and replace rules:', rules);
        // This would need to be implemented to search and replace text in the editor
    };

    // Don't render toolbar if editor is not available
    if (!editor) {
        return (
            <div className="toolbar">
                <div style={{ padding: '1rem', color: '#666' }}>
                    Loading editor...
                </div>
            </div>
        );
    }

    return (
        <div className="toolbar">
            {/* Block Type Selector */}
            <select
                value={blockType}
                onChange={(e) => handleBlockTypeChange(e.target.value)}
                className="toolbar-select"
            >
                <option value="paragraph">Paragraph</option>
                <option value="h1">Heading 1</option>
                <option value="h2">Heading 2</option>
                <option value="h3">Heading 3</option>
                <option value="h4">Heading 4</option>
                <option value="h5">Heading 5</option>
                <option value="h6">Heading 6</option>
                <option value="quote">Quote</option>
                <option value="code">Code Block</option>
            </select>

            {/* Font Family */}
            <select
                value={fontFamily}
                onChange={(e) => setFontFamily(e.target.value)}
                className="toolbar-select"
            >
                <option value="Arial">Arial</option>
                <option value="Georgia">Georgia</option>
                <option value="Times New Roman">Times New Roman</option>
                <option value="Helvetica">Helvetica</option>
                <option value="Courier New">Courier New</option>
                <option value="Verdana">Verdana</option>
            </select>

            {/* Font Size */}
            <select
                value={fontSize}
                onChange={(e) => setFontSize(e.target.value)}
                className="toolbar-select"
            >
                <option value="12px">12px</option>
                <option value="14px">14px</option>
                <option value="16px">16px</option>
                <option value="18px">18px</option>
                <option value="20px">20px</option>
                <option value="24px">24px</option>
                <option value="32px">32px</option>
            </select>

            {/* Text Formatting */}
            <div className="toolbar-group">
                <button
                    className={`toolbar-button ${activeFormats.has('bold') ? 'active' : ''}`}
                    onClick={() => formatText('bold')}
                    title="Bold"
                >
                    <strong>B</strong>
                </button>
                <button
                    className={`toolbar-button ${activeFormats.has('italic') ? 'active' : ''}`}
                    onClick={() => formatText('italic')}
                    title="Italic"
                >
                    <em>I</em>
                </button>
                <button
                    className={`toolbar-button ${activeFormats.has('underline') ? 'active' : ''}`}
                    onClick={() => formatText('underline')}
                    title="Underline"
                >
                    <u>U</u>
                </button>
                <button
                    className={`toolbar-button ${activeFormats.has('strikethrough') ? 'active' : ''}`}
                    onClick={() => formatText('strikethrough')}
                    title="Strikethrough"
                >
                    <s>S</s>
                </button>
                <button
                    className={`toolbar-button ${activeFormats.has('subscript') ? 'active' : ''}`}
                    onClick={() => formatText('subscript')}
                    title="Subscript"
                >
                    X<sub>2</sub>
                </button>
                <button
                    className={`toolbar-button ${activeFormats.has('superscript') ? 'active' : ''}`}
                    onClick={() => formatText('superscript')}
                    title="Superscript"
                >
                    X<sup>2</sup>
                </button>
            </div>

            {/* Text Alignment */}
            <div className="toolbar-group">
                <button
                    className="toolbar-button"
                    onClick={() => formatElement('left')}
                    title="Align Left"
                >
                    ←
                </button>
                <button
                    className="toolbar-button"
                    onClick={() => formatElement('center')}
                    title="Align Center"
                >
                    ↔
                </button>
                <button
                    className="toolbar-button"
                    onClick={() => formatElement('right')}
                    title="Align Right"
                >
                    →
                </button>
                <button
                    className="toolbar-button"
                    onClick={() => formatElement('justify')}
                    title="Justify"
                >
                    ≡
                </button>
            </div>

            {/* Lists */}
            <div className="toolbar-group">
                <button
                    className="toolbar-button"
                    onClick={() => insertList(editor, 'bullet')}
                    title="Bullet List"
                >
                    •
                </button>
                <button
                    className="toolbar-button"
                    onClick={() => insertList(editor, 'number')}
                    title="Numbered List"
                >
                    1.
                </button>
            </div>

            {/* Indentation */}
            <div className="toolbar-group">
                <button
                    className="toolbar-button"
                    onClick={outdent}
                    title="Decrease Indent"
                >
                    ⇤
                </button>
                <button
                    className="toolbar-button"
                    onClick={indent}
                    title="Increase Indent"
                >
                    ⇥
                </button>
            </div>

            {/* Colors */}
            <div className="toolbar-group">
                <ColorPicker
                    value={textColor}
                    onChange={setTextColor}
                    title="Text Color"
                />
                <ColorPicker
                    value={backgroundColor}
                    onChange={setBackgroundColor}
                    title="Background Color"
                />
            </div>

            {/* Insert Elements */}
            <div className="toolbar-group">
                <button
                    className="toolbar-button"
                    onClick={() => insertLink(editor)}
                    title="Insert Link"
                >
                    🔗
                </button>
                <ImagePlugin />
                <button
                    className="toolbar-button"
                    onClick={() => setShowTableSelector(true)}
                    title="Insert Table"
                >
                    📊
                </button>
                <button
                    className="toolbar-button"
                    onClick={() => insertHorizontalRule(editor)}
                    title="Insert Horizontal Rule"
                >
                    ―
                </button>
            </div>

            {/* Undo/Redo */}
            <div className="toolbar-group">
                <button
                    className="toolbar-button"
                    onClick={undo}
                    title="Undo"
                >
                    ↶
                </button>
                <button
                    className="toolbar-button"
                    onClick={redo}
                    title="Redo"
                >
                    ↷
                </button>
            </div>

            {/* Advanced Tools */}
            <div className="toolbar-group">
                <button
                    className="toolbar-button"
                    onClick={() => setShowFindReplace(true)}
                    title="Find and Replace"
                >
                    🔍
                </button>
                <button
                    className="toolbar-button"
                    onClick={() => setShowCleaningOptions(true)}
                    title="Clean HTML"
                >
                    🧹
                </button>
            </div>

            {/* Clear */}
            <button
                className="toolbar-button clear-button"
                onClick={clearEditor}
                title="Clear All"
            >
                🗑️
            </button>

            {/* Modal Components */}
            {showTableSelector && (
                <TableSelector
                    onTableCreate={handleTableCreate}
                    onClose={() => setShowTableSelector(false)}
                />
            )}

            {showCleaningOptions && (
                <CleaningOptions
                    onClean={handleClean}
                    onClose={() => setShowCleaningOptions(false)}
                />
            )}

            {showFindReplace && (
                <FindReplace
                    onReplace={handleFindReplace}
                    onClose={() => setShowFindReplace(false)}
                />
            )}
        </div>
    );
};

export default Toolbar;