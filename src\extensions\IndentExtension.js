import { Extension } from '@tiptap/core';

export const IndentExtension = Extension.create({
    name: 'indent',

    addOptions() {
        return {
            types: ['paragraph', 'heading'],
            minLevel: 0,
            maxLevel: 8,
        };
    },

    addGlobalAttributes() {
        return [
            {
                types: this.options.types,
                attributes: {
                    indent: {
                        default: 0,
                        parseHTML: element => {
                            const style = element.getAttribute('style');
                            if (style) {
                                const match = style.match(/margin-left:\s*(\d+)px/);
                                if (match) {
                                    return Math.round(parseInt(match[1]) / 30);
                                }
                            }
                            return 0;
                        },
                        renderHTML: attributes => {
                            if (!attributes.indent) {
                                return {};
                            }
                            return {
                                style: `margin-left: ${attributes.indent * 30}px`,
                            };
                        },
                    },
                },
            },
        ];
    },

    addCommands() {
        return {
            indent: () => ({ tr, state, dispatch }) => {
                const { selection } = state;
                const { from, to } = selection;

                state.doc.nodesBetween(from, to, (node, pos) => {
                    if (this.options.types.includes(node.type.name)) {
                        const currentIndent = node.attrs.indent || 0;
                        if (currentIndent < this.options.maxLevel) {
                            tr.setNodeMarkup(pos, undefined, {
                                ...node.attrs,
                                indent: currentIndent + 1,
                            });
                        }
                    }
                });

                if (dispatch) {
                    dispatch(tr);
                }

                return true;
            },
            outdent: () => ({ tr, state, dispatch }) => {
                const { selection } = state;
                const { from, to } = selection;

                state.doc.nodesBetween(from, to, (node, pos) => {
                    if (this.options.types.includes(node.type.name)) {
                        const currentIndent = node.attrs.indent || 0;
                        if (currentIndent > this.options.minLevel) {
                            tr.setNodeMarkup(pos, undefined, {
                                ...node.attrs,
                                indent: currentIndent - 1,
                            });
                        }
                    }
                });

                if (dispatch) {
                    dispatch(tr);
                }

                return true;
            },
        };
    },

    addKeyboardShortcuts() {
        return {
            Tab: () => this.editor.commands.indent(),
            'Shift-Tab': () => this.editor.commands.outdent(),
        };
    },
});
