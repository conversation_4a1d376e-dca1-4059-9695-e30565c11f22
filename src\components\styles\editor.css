/* Editor Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
        'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

.editor-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.editor-header {
    background: #2c3e50;
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.editor-header h1 {
    margin: 0;
    font-size: 1.5rem;
}

.save-button {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.save-button:hover {
    background: #2980b9;
}

.editor-wrapper {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background-color: white;
}

.toolbar {
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    padding: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.toolbar-group {
    display: flex;
    gap: 2px;
    border-right: 1px solid #ddd;
    padding-right: 0.5rem;
    margin-right: 0.5rem;
}

.toolbar-group:last-child {
    border-right: none;
}

.toolbar-button {
    background: white;
    border: 1px solid #ddd;
    padding: 0.4rem 0.6rem;
    cursor: pointer;
    border-radius: 3px;
    font-size: 0.9rem;
    min-width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-button:hover {
    background: #e9ecef;
}

.toolbar-button.active {
    background: #007bff;
    color: white;
}

.clear-button {
    background: #dc3545;
    color: white;
    border: none;
}

.clear-button:hover {
    background: #c82333;
}

.toolbar-select {
    border: 1px solid #ddd;
    padding: 0.4rem;
    border-radius: 3px;
    font-size: 0.9rem;
    background: white;
    min-width: 100px;
}

.color-input {
    width: 30px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
    padding: 0;
}

.editor-content {
    position: relative;
    min-height: 400px;
}

.editor-input {
    padding: 1rem;
    min-height: 400px;
    outline: none;
    border: none;
    font-size: 16px;
    line-height: 1.5;
    font-family: inherit;
}

.editor-placeholder {
    position: absolute;
    top: 1rem;
    left: 1rem;
    color: #999;
    pointer-events: none;
    font-size: 16px;
}

/* Lexical Editor Styles */
.editor-root {
    position: relative;
}

.editor-paragraph {
    margin: 0 0 8px 0;
}

.editor-heading-h1 {
    font-size: 2em;
    font-weight: bold;
    margin: 16px 0 8px 0;
    color: #333;
}

.editor-heading-h2 {
    font-size: 1.5em;
    font-weight: bold;
    margin: 14px 0 6px 0;
    color: #333;
}

.editor-heading-h3 {
    font-size: 1.25em;
    font-weight: bold;
    margin: 12px 0 6px 0;
    color: #333;
}

.editor-heading-h4 {
    font-size: 1.1em;
    font-weight: bold;
    margin: 10px 0 4px 0;
    color: #333;
}

.editor-heading-h5 {
    font-size: 1em;
    font-weight: bold;
    margin: 8px 0 4px 0;
    color: #333;
}

.editor-heading-h6 {
    font-size: 0.9em;
    font-weight: bold;
    margin: 6px 0 4px 0;
    color: #333;
}

.editor-list-ol {
    margin: 8px 0;
    padding-left: 20px;
}

.editor-list-ul {
    margin: 8px 0;
    padding-left: 20px;
}

.editor-listitem {
    margin: 4px 0;
}

.editor-nested-listitem {
    list-style-type: none;
}

.editor-link {
    color: #007bff;
    text-decoration: underline;
    cursor: pointer;
}

.editor-link:hover {
    color: #0056b3;
}

.editor-text-bold {
    font-weight: bold;
}

.editor-text-italic {
    font-style: italic;
}

.editor-text-underline {
    text-decoration: underline;
}

.editor-text-strikethrough {
    text-decoration: line-through;
}

.editor-text-underlineStrikethrough {
    text-decoration: underline line-through;
}

.editor-text-code {
    background-color: #f1f3f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.editor-text-subscript {
    font-size: 0.8em;
    vertical-align: sub;
}

.editor-text-superscript {
    font-size: 0.8em;
    vertical-align: super;
}

.editor-code {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    margin: 8px 0;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    overflow-x: auto;
}

.editor-quote {
    border-left: 4px solid #007bff;
    padding-left: 16px;
    margin: 16px 0;
    font-style: italic;
    color: #666;
}

.editor-table {
    border-collapse: collapse;
    width: 100%;
    margin: 16px 0;
    border: 1px solid #ddd;
}

.editor-table-cell {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: left;
}

.editor-table-cell-header {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: left;
    background-color: #f8f9fa;
    font-weight: bold;
}

@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        align-items: flex-start;
    }

    .toolbar-group {
        margin-bottom: 0.5rem;
    }

    .editor-input {
        padding: 0.5rem;
        font-size: 14px;
    }
}