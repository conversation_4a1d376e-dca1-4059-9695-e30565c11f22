/* Editor Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
        'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

.editor-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.editor-header {
    background: #2c3e50;
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.editor-header h1 {
    margin: 0;
    font-size: 1.5rem;
}

.save-button {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.save-button:hover {
    background: #2980b9;
}

.clean-button {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.clean-button:hover {
    background: #c0392b;
}

.editor-wrapper {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background-color: white;
}

.toolbar {
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    padding: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.toolbar-group {
    display: flex;
    gap: 2px;
    border-right: 1px solid #ddd;
    padding-right: 0.5rem;
    margin-right: 0.5rem;
}

.toolbar-group:last-child {
    border-right: none;
}

.toolbar-button {
    background: white;
    border: 1px solid #ddd;
    padding: 0.4rem 0.6rem;
    cursor: pointer;
    border-radius: 3px;
    font-size: 0.9rem;
    min-width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-button:hover {
    background: #e9ecef;
}

.toolbar-button.active {
    background: #007bff;
    color: white;
}

.clear-button {
    background: #dc3545;
    color: white;
    border: none;
}

.clear-button:hover {
    background: #c82333;
}

.toolbar-select {
    border: 1px solid #ddd;
    padding: 0.4rem;
    border-radius: 3px;
    font-size: 0.9rem;
    background: white;
    min-width: 100px;
}

.color-input {
    width: 30px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
    padding: 0;
}

/* Split Screen Layout */
.editor-split-view {
    display: flex;
    height: 600px;
    border-top: 1px solid #ddd;
}

.editor-panel,
.preview-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #ddd;
}

.preview-panel {
    border-right: none;
}

.panel-header {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 0.9rem;
    color: #495057;
}

.preview-controls {
    display: flex;
    gap: 0.5rem;
}

.view-source-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    cursor: pointer;
}

.view-source-btn:hover {
    background: #5a6268;
}

.editor-content {
    position: relative;
    flex: 1;
    overflow: auto;
}

.preview-content {
    flex: 1;
    background: white;
}

.preview-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
}

.editor-input {
    padding: 1rem;
    min-height: 400px;
    outline: none;
    border: none;
    font-size: 16px;
    line-height: 1.5;
    font-family: inherit;
}

.editor-placeholder {
    position: absolute;
    top: 1rem;
    left: 1rem;
    color: #999;
    pointer-events: none;
    font-size: 16px;
}

/* Lexical Editor Styles */
.editor-root {
    position: relative;
}

.editor-paragraph {
    margin: 0 0 8px 0;
}

.editor-heading-h1 {
    font-size: 2em;
    font-weight: bold;
    margin: 16px 0 8px 0;
    color: #333;
}

.editor-heading-h2 {
    font-size: 1.5em;
    font-weight: bold;
    margin: 14px 0 6px 0;
    color: #333;
}

.editor-heading-h3 {
    font-size: 1.25em;
    font-weight: bold;
    margin: 12px 0 6px 0;
    color: #333;
}

.editor-heading-h4 {
    font-size: 1.1em;
    font-weight: bold;
    margin: 10px 0 4px 0;
    color: #333;
}

.editor-heading-h5 {
    font-size: 1em;
    font-weight: bold;
    margin: 8px 0 4px 0;
    color: #333;
}

.editor-heading-h6 {
    font-size: 0.9em;
    font-weight: bold;
    margin: 6px 0 4px 0;
    color: #333;
}

.editor-list-ol {
    margin: 8px 0;
    padding-left: 20px;
}

.editor-list-ul {
    margin: 8px 0;
    padding-left: 20px;
}

.editor-listitem {
    margin: 4px 0;
}

.editor-nested-listitem {
    list-style-type: none;
}

.editor-link {
    color: #007bff;
    text-decoration: underline;
    cursor: pointer;
}

.editor-link:hover {
    color: #0056b3;
}

.editor-text-bold {
    font-weight: bold;
}

.editor-text-italic {
    font-style: italic;
}

.editor-text-underline {
    text-decoration: underline;
}

.editor-text-strikethrough {
    text-decoration: line-through;
}

.editor-text-underlineStrikethrough {
    text-decoration: underline line-through;
}

.editor-text-code {
    background-color: #f1f3f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.editor-text-subscript {
    font-size: 0.8em;
    vertical-align: sub;
}

.editor-text-superscript {
    font-size: 0.8em;
    vertical-align: super;
}

.editor-code {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    margin: 8px 0;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    overflow-x: auto;
}

.editor-quote {
    border-left: 4px solid #007bff;
    padding-left: 16px;
    margin: 16px 0;
    font-style: italic;
    color: #666;
}

.editor-table {
    border-collapse: collapse;
    width: 100%;
    margin: 16px 0;
    border: 1px solid #ddd;
}

.editor-table-cell {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: left;
}

.editor-table-cell-header {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: left;
    background-color: #f8f9fa;
    font-weight: bold;
}

/* Table Selector Styles */
.table-selector-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.table-selector {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 300px;
}

.table-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.table-selector-header h4 {
    margin: 0;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #333;
}

.table-grid {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-bottom: 1rem;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
}

.table-row {
    display: flex;
    gap: 2px;
}

.table-cell {
    width: 20px;
    height: 20px;
    border: 1px solid #ccc;
    background: white;
    cursor: pointer;
    transition: background-color 0.1s;
}

.table-cell:hover,
.table-cell.selected {
    background: #007bff;
}

.table-info {
    text-align: center;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 1rem;
}

.table-selector-footer {
    border-top: 1px solid #eee;
    padding-top: 1rem;
}

.custom-size {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.custom-size label {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.9rem;
}

.custom-size input {
    padding: 0.25rem;
    border: 1px solid #ddd;
    border-radius: 3px;
    width: 60px;
}

.create-table-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
}

.create-table-btn:hover {
    background: #0056b3;
}

/* Cleaning Options Styles */
.cleaning-options-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.cleaning-options {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
}

.cleaning-options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.cleaning-options-header h4 {
    margin: 0;
    color: #333;
}

.cleaning-options-content {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.cleaning-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.cleaning-option:last-child {
    border-bottom: none;
}

.cleaning-option input[type="checkbox"] {
    margin: 0;
}

.cleaning-option span {
    font-size: 0.9rem;
    color: #333;
}

.cleaning-options-footer {
    display: flex;
    gap: 0.5rem;
    border-top: 1px solid #eee;
    padding-top: 1rem;
}

.select-all-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    flex: 1;
}

.select-all-btn:hover {
    background: #5a6268;
}

.clean-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    flex: 1;
}

.clean-btn:hover {
    background: #c0392b;
}

/* Find and Replace Styles */
.find-replace-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.find-replace {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
}

.find-replace-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.find-replace-header h4 {
    margin: 0;
    color: #333;
}

.find-replace-content {
    margin-bottom: 1rem;
}

.replace-rules {
    margin-bottom: 1rem;
}

.replace-rule {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border: 1px solid #eee;
    border-radius: 4px;
}

.rule-number {
    background: #007bff;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.rule-inputs {
    flex: 1;
    display: flex;
    gap: 0.5rem;
}

.find-input,
.replace-input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 0.9rem;
}

.remove-rule-btn {
    background: #dc3545;
    color: white;
    border: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-rule-btn:hover {
    background: #c82333;
}

.add-rule-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
}

.add-rule-btn:hover {
    background: #218838;
}

.find-replace-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #eee;
    padding-top: 1rem;
}

.replace-info {
    font-size: 0.9rem;
    color: #666;
}

.replace-all-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
}

.replace-all-btn:hover {
    background: #0056b3;
}

/* TipTap Editor Styles */
.tiptap-editor {
    outline: none;
    padding: 1rem;
    min-height: 400px;
    font-family: Arial, sans-serif;
    line-height: 1.6;
}

.tiptap-editor p {
    margin: 0.5rem 0;
}

.tiptap-editor h1,
.tiptap-editor h2,
.tiptap-editor h3,
.tiptap-editor h4,
.tiptap-editor h5,
.tiptap-editor h6 {
    margin: 1rem 0 0.5rem 0;
    font-weight: bold;
}

.tiptap-editor h1 {
    font-size: 2rem;
}

.tiptap-editor h2 {
    font-size: 1.75rem;
}

.tiptap-editor h3 {
    font-size: 1.5rem;
}

.tiptap-editor h4 {
    font-size: 1.25rem;
}

.tiptap-editor h5 {
    font-size: 1.1rem;
}

.tiptap-editor h6 {
    font-size: 1rem;
}

.tiptap-editor ul,
.tiptap-editor ol {
    padding-left: 2rem;
    margin: 0.5rem 0;
}

.tiptap-editor blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: #666;
}

.tiptap-editor code {
    background-color: #f1f3f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

.tiptap-editor pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
    overflow-x: auto;
    margin: 1rem 0;
}

.tiptap-editor pre code {
    background: none;
    padding: 0;
}

.tiptap-editor table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
}

.tiptap-editor th,
.tiptap-editor td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.tiptap-editor th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.tiptap-editor img {
    max-width: 100%;
    height: auto;
    margin: 0.5rem 0;
}

.tiptap-editor a {
    color: #007bff;
    text-decoration: underline;
}

.tiptap-editor a:hover {
    text-decoration: none;
}

/* Preview HTML Styles */
.preview-html {
    font-family: Arial, sans-serif;
    line-height: 1.6;
}

.preview-html table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
}

.preview-html th,
.preview-html td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.preview-html th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.preview-html blockquote {
    border-left: 4px solid #007bff;
    padding-left: 16px;
    margin: 16px 0;
    font-style: italic;
    color: #666;
}

.preview-html code {
    background-color: #f1f3f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

.preview-html pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    overflow-x: auto;
    margin: 1rem 0;
}

.preview-html pre code {
    background: none;
    padding: 0;
}

.preview-html img {
    max-width: 100%;
    height: auto;
    margin: 0.5rem 0;
}

.preview-html a {
    color: #007bff;
    text-decoration: underline;
}

.preview-html ul,
.preview-html ol {
    padding-left: 2rem;
    margin: 0.5rem 0;
}

.preview-html h1,
.preview-html h2,
.preview-html h3,
.preview-html h4,
.preview-html h5,
.preview-html h6 {
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        align-items: flex-start;
    }

    .toolbar-group {
        margin-bottom: 0.5rem;
    }

    .editor-input {
        padding: 0.5rem;
        font-size: 14px;
    }

    .editor-split-view {
        flex-direction: column;
        height: auto;
    }

    .editor-panel,
    .preview-panel {
        border-right: none;
        border-bottom: 1px solid #ddd;
    }

    .preview-panel {
        border-bottom: none;
    }
}