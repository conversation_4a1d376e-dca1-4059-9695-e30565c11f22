/* Editor Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
        'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

.editor-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-height: 100vh;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e0e0e0;
}

.editor-header h1 {
    color: #333;
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.save-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.save-button:hover {
    background-color: #0056b3;
}

.editor-wrapper {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background-color: white;
}

.toolbar {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    min-height: 60px;
}

.toolbar-group {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0 8px;
    border-right: 1px solid #ddd;
}

.toolbar-group:last-child {
    border-right: none;
}

.toolbar-button {
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 14px;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.toolbar-button:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.toolbar-button.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.toolbar-button.clear-button {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
    margin-left: auto;
}

.toolbar-button.clear-button:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.toolbar-select {
    padding: 6px 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
    min-width: 120px;
    height: 32px;
}

.color-input {
    width: 32px;
    height: 32px;
    border: 1px solid #ccc;
    border-radius: 4px;
    cursor: pointer;
    padding: 0;
    background: none;
}

.editor-content {
    position: relative;
    background-color: white;
}

.editor-input {
    min-height: 400px;
    padding: 20px;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    outline: none;
    border: none;
    resize: none;
    caret-color: #007bff;
}

.editor-placeholder {
    position: absolute;
    top: 20px;
    left: 20px;
    color: #999;
    font-size: 16px;
    pointer-events: none;
    user-select: none;
}

/* Lexical Editor Styles */
.editor-root {
    position: relative;
}

.editor-paragraph {
    margin: 0 0 8px 0;
}

.editor-heading-h1 {
    font-size: 2em;
    font-weight: bold;
    margin: 16px 0 8px 0;
    color: #333;
}

.editor-heading-h2 {
    font-size: 1.5em;
    font-weight: bold;
    margin: 14px 0 6px 0;
    color: #333;
}

.editor-heading-h3 {
    font-size: 1.25em;
    font-weight: bold;
    margin: 12px 0 6px 0;
    color: #333;
}

.editor-heading-h4 {
    font-size: 1.1em;
    font-weight: bold;
    margin: 10px 0 4px 0;
    color: #333;
}

.editor-heading-h5 {
    font-size: 1em;
    font-weight: bold;
    margin: 8px 0 4px 0;
    color: #333;
}

.editor-heading-h6 {
    font-size: 0.9em;
    font-weight: bold;
    margin: 6px 0 4px 0;
    color: #333;
}

.editor-list-ol {
    margin: 8px 0;
    padding-left: 20px;
}

.editor-list-ul {
    margin: 8px 0;
    padding-left: 20px;
}

.editor-listitem {
    margin: 4px 0;
}

.editor-nested-listitem {
    list-style-type: none;
}

.editor-link {
    color: #007bff;
    text-decoration: underline;
    cursor: pointer;
}

.editor-link:hover {
    color: #0056b3;
}

.editor-text-bold {
    font-weight: bold;
}

.editor-text-italic {
    font-style: italic;
}

.editor-text-underline {
    text-decoration: underline;
}

.editor-text-strikethrough {
    text-decoration: line-through;
}

.editor-text-underlineStrikethrough {
    text-decoration: underline line-through;
}

.editor-text-code {
    background-color: #f1f3f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.editor-text-subscript {
    font-size: 0.8em;
    vertical-align: sub;
}

.editor-text-superscript {
    font-size: 0.8em;
    vertical-align: super;
}

.editor-code {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    margin: 8px 0;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    overflow-x: auto;
}

.editor-quote {
    border-left: 4px solid #007bff;
    padding-left: 16px;
    margin: 16px 0;
    font-style: italic;
    color: #666;
}

.editor-table {
    border-collapse: collapse;
    width: 100%;
    margin: 16px 0;
    border: 1px solid #ddd;
}

.editor-table-cell {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: left;
}

.editor-table-cell-header {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: left;
    background-color: #f8f9fa;
    font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
    .editor-container {
        padding: 10px;
        margin: 0;
        border-radius: 0;
    }

    .toolbar {
        padding: 8px;
        gap: 4px;
    }

    .toolbar-group {
        padding: 0 4px;
    }

    .toolbar-select {
        min-width: 80px;
        font-size: 12px;
    }

    .toolbar-button {
        min-width: 28px;
        height: 28px;
        font-size: 12px;
        padding: 4px 6px;
    }

    .editor-input {
        padding: 15px;
        font-size: 14px;
    }

    .editor-placeholder {
        top: 15px;
        left: 15px;
        font-size: 14px;
    }
}