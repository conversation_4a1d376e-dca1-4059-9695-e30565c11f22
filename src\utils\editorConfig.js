import { CodeHighlightNode, CodeNode } from '@lexical/code';
import { AutoLinkNode, LinkNode } from '@lexical/link';
import { ListItemNode, ListNode } from '@lexical/list';
// Note: HorizontalRuleNode might not be available in this version
// import { HorizontalRuleNode } from '@lexical/react/LexicalHorizontalRuleNode';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { TableCellNode, TableNode, TableRowNode } from '@lexical/table';

// Editor Configuration
export const editorConfig = {
    namespace: 'LexicalEditor',
    theme: {
        root: 'editor-root',
        paragraph: 'editor-paragraph',
        heading: {
            h1: 'editor-heading-h1',
            h2: 'editor-heading-h2',
            h3: 'editor-heading-h3',
            h4: 'editor-heading-h4',
            h5: 'editor-heading-h5',
            h6: 'editor-heading-h6'
        },
        list: {
            nested: {
                listitem: 'editor-nested-listitem'
            },
            ol: 'editor-list-ol',
            ul: 'editor-list-ul',
            listitem: 'editor-listitem'
        },
        link: 'editor-link',
        text: {
            bold: 'editor-text-bold',
            italic: 'editor-text-italic',
            underline: 'editor-text-underline',
            strikethrough: 'editor-text-strikethrough',
            underlineStrikethrough: 'editor-text-underlineStrikethrough',
            code: 'editor-text-code',
            subscript: 'editor-text-subscript',
            superscript: 'editor-text-superscript'
        },
        code: 'editor-code',
        codeHighlight: {},
        table: 'editor-table',
        tableCell: 'editor-table-cell',
        tableCellHeader: 'editor-table-cell-header',
        quote: 'editor-quote'
    },
    nodes: [
        HeadingNode,
        ListNode,
        ListItemNode,
        QuoteNode,
        CodeNode,
        CodeHighlightNode,
        TableNode,
        TableCellNode,
        TableRowNode,
        LinkNode,
        AutoLinkNode
        // HorizontalRuleNode // Commented out as it might not be available
    ],
    onError: (error) => {
        console.error('Lexical Error:', error);
    }
};