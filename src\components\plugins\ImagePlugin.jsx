import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import React from 'react';
import { handleFileUpload, insertImage } from '../../utils/editorUtils';
import FileUpload from '../UI/FileUpload';

const ImagePlugin = () => {
    const [editor] = useLexicalComposerContext();

    const handleImageUpload = (file) => {
        handleFileUpload(editor, file);
    };

    const handleImageByURL = () => {
        insertImage(editor);
    };

    return (
        <div className="toolbar-group">
            <button
                className="toolbar-button"
                onClick={handleImageByURL}
                title="Insert Image by URL"
            >
                🖼️
            </button>
            <FileUpload onFileSelect={handleImageUpload}>
                📁
            </FileUpload>
        </div>
    );
};

export default ImagePlugin;