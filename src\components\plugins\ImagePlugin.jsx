import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import React from 'react';
import { handleFileUpload, insertImage } from '../../utils/editorUtils';
import FileUpload from '../UI/FileUpload';

const ImagePlugin = () => {
    const [editor] = useLexicalComposerContext();

    const handleImageUpload = (file) => {
        if (!editor) return;
        handleFileUpload(editor, file);
    };

    const handleImageByURL = () => {
        if (!editor) return;
        insertImage(editor);
    };

    return (
        <div className="toolbar-group">
            <button
                className="toolbar-button"
                onClick={handleImageByURL}
                title="Insert Image by URL"
            >
                🖼️
            </button>
            <FileUpload onFileSelect={handleImageUpload}>
                📁
            </FileUpload>
        </div>
    );
};

export default ImagePlugin;