import { $createCodeNode } from '@lexical/code';
import { $createLinkNode } from '@lexical/link';
import { $createListItemNode, $createListNode } from '@lexical/list';
// import { $createHorizontalRuleNode } from '@lexical/react/LexicalHorizontalRuleNode';
import { $createHeadingNode, $createQuoteNode } from '@lexical/rich-text';
import { $createTableNodeWithDimensions } from '@lexical/table';
import { $insertNodeToNearestRoot } from '@lexical/utils';
import {
    $createTextNode,
    $getSelection,
    $isRangeSelection
} from 'lexical';

// Helper function to save content as HTML
export const saveAsHTML = (htmlContent) => {
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'document.html';
    a.click();
    URL.revokeObjectURL(url);
};

// Helper function to insert heading
export const insertHeading = (editor, headingSize) => {
    editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
            const heading = $createHeadingNode(headingSize);
            selection.insertNodes([heading]);
        }
    });
};

// Helper function to insert list
export const insertList = (editor, listType) => {
    editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
            const list = $createListNode(listType);
            const listItem = $createListItemNode();
            list.append(listItem);
            selection.insertNodes([list]);
        }
    });
};

// Helper function to insert quote
export const insertQuote = (editor) => {
    editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
            const quote = $createQuoteNode();
            selection.insertNodes([quote]);
        }
    });
};

// Helper function to insert code block
export const insertCode = (editor) => {
    editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
            const code = $createCodeNode();
            selection.insertNodes([code]);
        }
    });
};

// Helper function to insert horizontal rule
export const insertHorizontalRule = (editor) => {
    editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
            // Insert a simple horizontal rule as text for now
            selection.insertRawText('\n---\n');
        }
    });
};

// Helper function to insert table
export const insertTable = (editor) => {
    editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
            const table = $createTableNodeWithDimensions(3, 3, false);
            $insertNodeToNearestRoot(table);
        }
    });
};

// Helper function to insert link
export const insertLink = (editor) => {
    const url = prompt('Enter URL:');
    if (url) {
        editor.update(() => {
            const selection = $getSelection();
            if ($isRangeSelection(selection)) {
                const link = $createLinkNode(url);
                const text = $createTextNode('Link');
                link.append(text);
                selection.insertNodes([link]);
            }
        });
    }
};

// Helper function to insert image by URL
export const insertImage = (editor) => {
    const url = prompt('Enter image URL:');
    if (url) {
        editor.update(() => {
            const selection = $getSelection();
            if ($isRangeSelection(selection)) {
                selection.insertRawText(`<img src="${url}" style="max-width: 100%; height: auto;" alt="Image" />`);
            }
        });
    }
};

// Helper function to handle file upload
export const handleFileUpload = (editor, file) => {
    if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const dataURL = e.target.result;
            editor.update(() => {
                const selection = $getSelection();
                if ($isRangeSelection(selection)) {
                    selection.insertRawText(`<img src="${dataURL}" style="max-width: 100%; height: auto;" alt="Uploaded Image" />`);
                }
            });
        };
        reader.readAsDataURL(file);
    }
};