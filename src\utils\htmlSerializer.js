import { $getRoot } from 'lexical';

// Custom HTML serializer for Lexical editor content
export const generateHTMLFromEditor = (editor) => {
    if (!editor) return '';
    
    let htmlContent = '';
    
    editor.getEditorState().read(() => {
        const root = $getRoot();
        htmlContent = serializeNode(root);
    });
    
    return htmlContent;
};

const serializeNode = (node) => {
    const children = node.getChildren();
    let html = '';
    
    for (const child of children) {
        html += serializeNodeToHTML(child);
    }
    
    return html;
};

const serializeNodeToHTML = (node) => {
    const type = node.getType();
    const children = node.getChildren();
    
    switch (type) {
        case 'paragraph': {
            const childrenHTML = children.map(child => serializeNodeToHTML(child)).join('');
            return childrenHTML ? `<p>${childrenHTML}</p>` : '<p><br></p>';
        }
        
        case 'heading': {
            const tag = node.getTag();
            const childrenHTML = children.map(child => serializeNodeToHTML(child)).join('');
            return `<${tag}>${childrenHTML}</${tag}>`;
        }
        
        case 'list': {
            const listType = node.getListType();
            const tag = listType === 'bullet' ? 'ul' : 'ol';
            const childrenHTML = children.map(child => serializeNodeToHTML(child)).join('');
            return `<${tag}>${childrenHTML}</${tag}>`;
        }
        
        case 'listitem': {
            const childrenHTML = children.map(child => serializeNodeToHTML(child)).join('');
            return `<li>${childrenHTML}</li>`;
        }
        
        case 'quote': {
            const childrenHTML = children.map(child => serializeNodeToHTML(child)).join('');
            return `<blockquote>${childrenHTML}</blockquote>`;
        }
        
        case 'code': {
            const textContent = node.getTextContent();
            return `<pre><code>${escapeHTML(textContent)}</code></pre>`;
        }
        
        case 'link': {
            const url = node.getURL();
            const childrenHTML = children.map(child => serializeNodeToHTML(child)).join('');
            return `<a href="${escapeHTML(url)}">${childrenHTML}</a>`;
        }
        
        case 'text': {
            let text = escapeHTML(node.getTextContent());
            
            // Apply text formatting
            if (node.hasFormat('bold')) {
                text = `<strong>${text}</strong>`;
            }
            if (node.hasFormat('italic')) {
                text = `<em>${text}</em>`;
            }
            if (node.hasFormat('underline')) {
                text = `<u>${text}</u>`;
            }
            if (node.hasFormat('strikethrough')) {
                text = `<s>${text}</s>`;
            }
            if (node.hasFormat('code')) {
                text = `<code>${text}</code>`;
            }
            if (node.hasFormat('subscript')) {
                text = `<sub>${text}</sub>`;
            }
            if (node.hasFormat('superscript')) {
                text = `<sup>${text}</sup>`;
            }
            
            return text;
        }
        
        case 'linebreak': {
            return '<br>';
        }
        
        case 'table': {
            const childrenHTML = children.map(child => serializeNodeToHTML(child)).join('');
            return `<table>${childrenHTML}</table>`;
        }
        
        case 'tablerow': {
            const childrenHTML = children.map(child => serializeNodeToHTML(child)).join('');
            return `<tr>${childrenHTML}</tr>`;
        }
        
        case 'tablecell': {
            const childrenHTML = children.map(child => serializeNodeToHTML(child)).join('');
            const tag = node.getHeaderStyles() ? 'th' : 'td';
            return `<${tag}>${childrenHTML}</${tag}>`;
        }
        
        default: {
            // For unknown node types, try to serialize children
            if (children.length > 0) {
                return children.map(child => serializeNodeToHTML(child)).join('');
            }
            return '';
        }
    }
};

const escapeHTML = (text) => {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
};

// Alternative simpler approach for basic HTML generation
export const generateBasicHTML = (editor) => {
    if (!editor) return '';
    
    let htmlContent = '';
    
    editor.getEditorState().read(() => {
        const root = $getRoot();
        const textContent = root.getTextContent();
        
        if (textContent.trim()) {
            // Split by double newlines for paragraphs
            const paragraphs = textContent.split('\n\n').filter(p => p.trim());
            if (paragraphs.length > 0) {
                htmlContent = paragraphs.map(p => {
                    // Handle single line breaks within paragraphs
                    const lines = p.split('\n').filter(line => line.trim());
                    const content = lines.join('<br>');
                    return `<p>${escapeHTML(content)}</p>`;
                }).join('');
            } else {
                // Single paragraph
                const lines = textContent.split('\n').filter(line => line.trim());
                if (lines.length > 0) {
                    const content = lines.join('<br>');
                    htmlContent = `<p>${escapeHTML(content)}</p>`;
                }
            }
        }
    });
    
    return htmlContent;
};
